'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/goods-receipts/data-table';
import { createGoodsReceiptColumns } from '@/components/goods-receipts/columns';
import { GoodsReceiptQuickView } from '@/components/goods-receipts/goods-receipt-quick-view';

import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { GoodsReceiptQueryParams, GoodsReceiptWithRelations } from '@/types/goods-receipt';
import {
  useGoodsReceipts,
  useGoodsReceipt,
  useGoodsReceiptsInvalidate,
  useRejectGoodsReceipt,
  useCompleteGoodsReceipt,
  useDeleteGoodsReceipt,
  useExportGoodsReceipts,
} from '@/hooks/useGoodsReceipts';

interface GoodsReceiptsPageClientProps {
  initialQuery: GoodsReceiptQueryParams;
}

export function GoodsReceiptsPageClient({ initialQuery }: GoodsReceiptsPageClientProps) {
  const router = useRouter();
  const [query, setQuery] = useState<GoodsReceiptQueryParams>(initialQuery);
  const [selectedGoodsReceiptId, setSelectedGoodsReceiptId] = useState<string | null>(null);
  const [showQuickView, setShowQuickView] = useState(false);

  const [deleteGoodsReceipt, setDeleteGoodsReceipt] = useState<GoodsReceiptWithRelations | null>(null);
  const [rejectGoodsReceipt, setRejectGoodsReceipt] = useState<GoodsReceiptWithRelations | null>(null);

  // Queries and mutations
  const { data: goodsReceiptsResponse, isLoading, error } = useGoodsReceipts(query);
  const { data: selectedGoodsReceipt } = useGoodsReceipt(selectedGoodsReceiptId!);
  const invalidateGoodsReceipts = useGoodsReceiptsInvalidate();
  const rejectGoodsReceiptMutation = useRejectGoodsReceipt();
  const completeGoodsReceiptMutation = useCompleteGoodsReceipt();
  const deleteGoodsReceiptMutation = useDeleteGoodsReceipt();
  const exportGoodsReceiptsMutation = useExportGoodsReceipts();

  const goodsReceipts = goodsReceiptsResponse?.data || [];
  const meta = goodsReceiptsResponse?.meta;

  // Update query parameters - similar to inventory pattern
  const updateQuery = useCallback((newQuery: GoodsReceiptQueryParams) => {
    setQuery(newQuery);

    // Update URL without navigation to preserve scroll position
    const params = new URLSearchParams();
    Object.entries(newQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });

    const newUrl = `/dashboard/goods-receipts?${params.toString()}`;
    const currentUrl = window.location.pathname + window.location.search;

    if (newUrl !== currentUrl) {
      window.history.replaceState(null, '', newUrl);
    }
  }, []);

  // Filter handlers - similar to inventory pattern
  const handleFilterChange = useCallback((key: keyof GoodsReceiptQueryParams, value: any) => {
    const newFilters = {
      ...query,
      [key]: value,
      page: 1, // Reset to first page when filtering
    };

    updateQuery(newFilters);
  }, [query, updateQuery]);

  const handleBatchFilterChange = useCallback((updates: Partial<GoodsReceiptQueryParams>) => {
    const newFilters = {
      ...query,
      ...updates,
      page: 1, // Reset to first page when filtering
    };

    updateQuery(newFilters);
  }, [query, updateQuery]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      page: 1,
      limit: 10,
      search: undefined, // Clear search field
    };
    updateQuery(clearedFilters);
  }, [updateQuery]);

  // Handlers
  const handleView = useCallback((goodsReceipt: GoodsReceiptWithRelations) => {
    setSelectedGoodsReceiptId(goodsReceipt.id);
    setShowQuickView(true);
  }, []);

  const handleViewDetails = useCallback((goodsReceipt: GoodsReceiptWithRelations) => {
    router.push(`/dashboard/goods-receipts/${goodsReceipt.id}`);
  }, [router]);

  const handleEdit = useCallback((goodsReceipt: GoodsReceiptWithRelations) => {
    router.push(`/dashboard/goods-receipts/${goodsReceipt.id}/edit`);
  }, [router]);

  const handleDelete = useCallback((goodsReceipt: GoodsReceiptWithRelations) => {
    setDeleteGoodsReceipt(goodsReceipt);
  }, []);



  const handleReject = useCallback((goodsReceipt: GoodsReceiptWithRelations) => {
    setRejectGoodsReceipt(goodsReceipt);
  }, []);

  const handleComplete = useCallback(async (goodsReceipt: GoodsReceiptWithRelations) => {
    try {
      await completeGoodsReceiptMutation.mutateAsync(goodsReceipt.id);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [completeGoodsReceiptMutation]);



  const handleRefresh = useCallback(() => {
    invalidateGoodsReceipts();
  }, [invalidateGoodsReceipts]);

  const handleExport = useCallback(async () => {
    try {
      await exportGoodsReceiptsMutation.mutateAsync(query);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [exportGoodsReceiptsMutation, query]);

  const handleNewGoodsReceipt = useCallback(() => {
    router.push('/dashboard/goods-receipts/new');
  }, [router]);

  const handleGoToPage = useCallback(async (routerPath: string) => {
    router.push(routerPath);
  }, [])

  const confirmDelete = useCallback(async () => {
    if (!deleteGoodsReceipt) return;

    try {
      await deleteGoodsReceiptMutation.mutateAsync(deleteGoodsReceipt.id);
      setDeleteGoodsReceipt(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [deleteGoodsReceipt, deleteGoodsReceiptMutation]);

  const confirmReject = useCallback(async () => {
    if (!rejectGoodsReceipt) return;

    try {
      await rejectGoodsReceiptMutation.mutateAsync(rejectGoodsReceipt.id);
      setRejectGoodsReceipt(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  }, [rejectGoodsReceipt, rejectGoodsReceiptMutation]);

  // Create columns with handlers
  const columns = createGoodsReceiptColumns({
    onView: handleView,
    onEdit: handleEdit,
    onDelete: handleDelete,
    onReject: handleReject,
    onComplete: handleComplete,
    goToPage: handleGoToPage,
  });

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Gagal memuat data penerimaan barang</p>
            <Button onClick={handleRefresh} variant="outline" className="mt-2">
              <RefreshCw className="mr-2 h-4 w-4" />
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {/* Header Actions */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Penerimaan Barang</h2>
            <p className="text-muted-foreground">
              Kelola penerimaan barang dan kontrol kualitas
            </p>
          </div>
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button onClick={handleNewGoodsReceipt} size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Penerimaan Baru
            </Button>
          </div>
        </div>

        {/* Data Table */}
        <Card className="w-full min-w-0 max-w-full overflow-hidden">
          <CardContent className="">
            <div className="w-full min-w-0 max-w-full">
              <DataTable
                columns={columns}
                data={goodsReceipts}
                meta={meta || { total: 0, page: 1, limit: 10, totalPages: 0, hasNextPage: false, hasPreviousPage: false }}
                query={query}
                onQueryChange={updateQuery}
                loading={isLoading}
                searchPlaceholder="Cari nomor penerimaan, supplier..."
                onRowClick={handleView}
                filters={query}
                onFilterChange={handleFilterChange}
                onBatchFilterChange={handleBatchFilterChange}
                onClearFilters={clearFilters}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick View Modal */}
      <GoodsReceiptQuickView
        goodsReceipt={selectedGoodsReceipt || null}
        open={showQuickView}
        onOpenChange={(open) => {
          setShowQuickView(open);
          if (!open) {
            setSelectedGoodsReceiptId(null);
          }
        }}
        onEdit={handleEdit}
        onReject={handleReject}
        onComplete={handleComplete}
        onDelete={handleDelete}
        onViewDetails={handleViewDetails}
      />



      {/* Delete Confirmation */}
      <AlertDialogWrapper
        open={!!deleteGoodsReceipt}
        onOpenChange={(open) => !open && setDeleteGoodsReceipt(null)}
        title="Hapus Penerimaan Barang"
        description={`Apakah Anda yakin ingin menghapus penerimaan barang ${deleteGoodsReceipt?.receiptNumber}? Tindakan ini tidak dapat dibatalkan.`}
        confirmText="Hapus"
        cancelText="Batal"
        onConfirm={confirmDelete}
        variant="destructive"
      />

      {/* Reject Confirmation */}
      <AlertDialogWrapper
        open={!!rejectGoodsReceipt}
        onOpenChange={(open) => !open && setRejectGoodsReceipt(null)}
        title="Tolak Penerimaan Barang"
        description={`Apakah Anda yakin ingin menolak penerimaan barang ${rejectGoodsReceipt?.receiptNumber}? Tindakan ini tidak dapat dibatalkan.`}
        confirmText="Tolak"
        cancelText="Batal"
        onConfirm={confirmReject}
        variant="destructive"
      />
    </>
  );
}
